# Archive Catalog for Initial Density Prediction Repository

## Summary

This document catalogs all Python files and other code files in the repository that are **NOT essential** to the main pipeline functionality and can be safely archived without breaking the core system.

### Main Pipeline Components (DO NOT ARCHIVE)
- `density_prediction_improved.py` - Primary pipeline file
- `density_prediction_plot_improved.py` - Plotting/visualization component
- `model.py` - MWLT Transformer architecture (required dependency)
- `utils.py` - Utility functions (required dependency)
- `dataset.py` - Dataset classes (required dependency)
- `__init__.py` - Module initialization (required dependency)

---

## Files Safe to Archive

### 1. Legacy/Alternative Pipeline Scripts
These are older or alternative implementations that are superseded by the improved pipeline:

#### `train.py`
- **Path**: `train.py`
- **Description**: Legacy training script using original dataset format
- **Reason for archiving**: Superseded by `density_prediction_improved.py` which includes enhanced training with better normalization, data augmentation, and improved loss functions
- **Dependencies**: Uses `model.py`, `utils.py`, `dataset.py` (all retained)

#### `test.py`
- **Path**: `test.py`
- **Description**: Legacy testing/evaluation script
- **Reason for archiving**: Superseded by evaluation functionality built into `density_prediction_improved.py`
- **Dependencies**: Uses `model.py`, `utils.py`, `dataset.py` (all retained)

#### `density_prediction_pipeline.py`
- **Path**: `density_prediction_pipeline.py`
- **Description**: Integrated pipeline combining training, testing, and visualization
- **Reason for archiving**: Superseded by `density_prediction_improved.py` + `density_prediction_plot_improved.py` combination which provides better functionality
- **Dependencies**: Uses `model.py`, `utils.py`, imports `plot_density_results.py` (legacy plotting)

#### `plot_density_results.py`
- **Path**: `plot_density_results.py`
- **Description**: Legacy plotting utilities for density prediction results
- **Reason for archiving**: Superseded by `density_prediction_plot_improved.py` which provides comprehensive visualization capabilities
- **Dependencies**: Uses `utils.py` (retained)

### 2. Test and Verification Scripts
These are development/testing scripts used for validation but not needed for production:

#### `verify_ml_architecture.py`
- **Path**: `verify_ml_architecture.py`
- **Description**: ML architecture verification script for testing model components
- **Reason for archiving**: Development/testing utility, not needed for production pipeline
- **Dependencies**: Uses `model.py`, `utils.py`, `density_prediction_improved.py` (all retained)

#### `test_checkpoint_fix.py`
- **Path**: `test_checkpoint_fix.py`
- **Description**: Test script to verify checkpoint saving functionality
- **Reason for archiving**: Development testing utility, functionality is now integrated
- **Dependencies**: Uses `utils.py` (retained)

#### `test_local_save_path.py`
- **Path**: `test_local_save_path.py`
- **Description**: Test script to verify local save path changes
- **Reason for archiving**: Development testing utility, functionality is now integrated
- **Dependencies**: Uses `density_prediction_improved.py`, `utils.py` (both retained)

### 3. Example and Setup Scripts
These are demonstration and setup utilities:

#### `example_usage.py`
- **Path**: `example_usage.py`
- **Description**: Example usage demonstrations for the density prediction module
- **Reason for archiving**: Documentation/example code, not essential for core functionality
- **Dependencies**: Uses all main pipeline components (all retained)

#### `setup.py`
- **Path**: `setup.py`
- **Description**: Module setup and installation script with import testing
- **Reason for archiving**: Setup utility, not needed once module is properly configured
- **Dependencies**: Tests imports from all main components (all retained)

### 4. Documentation Files
These are informational documents about fixes and changes:

#### `CHECKPOINT_SAVING_FIX.md`
- **Path**: `CHECKPOINT_SAVING_FIX.md`
- **Description**: Documentation about PyTorch checkpoint saving fix
- **Reason for archiving**: Historical documentation, fix is now integrated

#### `LOCAL_SAVE_PATH_CHANGES.md`
- **Path**: `LOCAL_SAVE_PATH_CHANGES.md`
- **Description**: Documentation about local save path changes
- **Reason for archiving**: Historical documentation, changes are now integrated

#### `CONSOLIDATION_SUMMARY.md`
- **Path**: `CONSOLIDATION_SUMMARY.md`
- **Description**: Summary of module consolidation process
- **Reason for archiving**: Historical documentation about development process

#### `ML_ARCHITECTURE_VERIFICATION.md`
- **Path**: `ML_ARCHITECTURE_VERIFICATION.md`
- **Description**: Documentation of ML architecture verification results
- **Reason for archiving**: Historical documentation, verification is complete

### 5. Cache and Build Artifacts
These are automatically generated files:

#### `__pycache__/` directory
- **Path**: `__pycache__/`
- **Description**: Python bytecode cache directory
- **Reason for archiving**: Automatically generated, can be recreated as needed
- **Contents**: 
  - `dataset.cpython-310.pyc`
  - `density_prediction_improved.cpython-310.pyc`
  - `density_prediction_plot_improved.cpython-310.pyc`
  - `model.cpython-310.pyc`
  - `utils.cpython-310.pyc`

---

## Verification Summary

✅ **Verified Safe to Archive**: All listed files have been analyzed for dependencies on the main pipeline components (`density_prediction_improved.py` and `density_prediction_plot_improved.py`).

✅ **No Breaking Dependencies**: None of the main pipeline files import or depend on any of the files listed for archiving.

✅ **Core Functionality Preserved**: The main pipeline consisting of:
- `density_prediction_improved.py` (training, evaluation, data processing)
- `density_prediction_plot_improved.py` (comprehensive visualization)
- `model.py` (MWLT Transformer architectures)
- `utils.py` (utility functions)
- `dataset.py` (dataset classes)
- `__init__.py` (module initialization)

Will remain fully functional after archiving all listed files.

---

## Recommended Archive Structure

Create an `archive/` directory and organize files by category:
```
archive/
├── legacy_scripts/
│   ├── train.py
│   ├── test.py
│   ├── density_prediction_pipeline.py
│   └── plot_density_results.py
├── test_scripts/
│   ├── verify_ml_architecture.py
│   ├── test_checkpoint_fix.py
│   └── test_local_save_path.py
├── examples_and_setup/
│   ├── example_usage.py
│   └── setup.py
├── documentation/
│   ├── CHECKPOINT_SAVING_FIX.md
│   ├── LOCAL_SAVE_PATH_CHANGES.md
│   ├── CONSOLIDATION_SUMMARY.md
│   └── ML_ARCHITECTURE_VERIFICATION.md
└── cache/
    └── __pycache__/
```

**Total files to archive**: 15 files + 1 directory (20 items total)
**Core files retained**: 6 essential files + data files + requirements.txt + README.md
